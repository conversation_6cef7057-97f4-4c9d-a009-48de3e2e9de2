"""
Global pytest configuration and fixtures for the <PERSON><PERSON>zi testing suite.
"""

import pytest
import asyncio
import os
import django

# Setup Django BEFORE importing any Django modules
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

# Now we can safely import Django modules
from django.test import TestCase
from django.contrib.auth import get_user_model
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import Room, RoomParticipant, RoomState, UserState
from events.models import EventTemplate, EventStep
from core.consumers import RoomConsumer

User = get_user_model()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def api_client():
    """Provide a DRF API client for testing."""
    return APIClient()


@pytest.fixture
@database_sync_to_async
def test_user():
    """Create a test user."""
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'password': 'testpass123',
            'subscription_level': 'Free'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    return user


@pytest.fixture
@database_sync_to_async
def test_host():
    """Create a test host user."""
    user, created = User.objects.get_or_create(
        username='testhost',
        defaults={
            'password': 'testpass123',
            'subscription_level': 'Pro'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    return user


@pytest.fixture
@database_sync_to_async
def test_template(test_host):
    """Create a test event template."""
    template, created = EventTemplate.objects.get_or_create(
        name='Test Template',
        defaults={
            'description': 'A template for testing',
            'creator': test_host
        }
    )
    
    # Add test steps if created
    if created:
        EventStep.objects.create(
            template=template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300,
            name='Pictionary Round'
        )
        EventStep.objects.create(
            template=template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=180,
            name='Free Chat'
        )
    
    return template


@pytest.fixture
@database_sync_to_async
def test_room(test_host, test_template):
    """Create a test room."""
    room, created = Room.objects.get_or_create(
        room_code='TEST01',
        defaults={
            'host': test_host,
            'event_template': test_template,
            'status': RoomState.WAITING,
            'max_participants': 10
        }
    )
    return room


@pytest.fixture
def authenticated_client(api_client, test_user):
    """Provide an authenticated API client."""
    refresh = RefreshToken.for_user(test_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
async def websocket_communicator(test_room):
    """Create a WebSocket communicator for testing."""
    communicator = WebsocketCommunicator(
        RoomConsumer.as_asgi(),
        f"/ws/room/{test_room.room_code}/"
    )
    
    # Connect
    connected, subprotocol = await communicator.connect()
    assert connected
    
    yield communicator
    
    # Disconnect
    await communicator.disconnect()


@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """
    Grant database access to all tests.
    """
    pass


@pytest.fixture
def clean_database():
    """Clean up test data after each test."""
    yield
    # Cleanup
    Room.objects.filter(room_code__startswith='TEST').delete()
    User.objects.filter(username__startswith='test').delete()
    EventTemplate.objects.filter(name__startswith='Test').delete()
