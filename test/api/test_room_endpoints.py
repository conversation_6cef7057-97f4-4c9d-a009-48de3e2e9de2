"""
API tests for room management endpoints.
"""

import pytest
import json
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import Room, RoomParticipant, RoomState, UserState
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.api
class TestRoomAPI:
    """Test room management API endpoints."""
    
    def setup_method(self):
        """Set up test data for each test method."""
        self.client = APIClient()
        
        # Create test users
        self.host_user = User.objects.create_user(
            username='host_user',
            password='testpass123',
            subscription_level='Pro'
        )
        
        self.participant_user = User.objects.create_user(
            username='participant_user',
            password='testpass123',
            subscription_level='Free'
        )
        
        # Create test template
        self.template = EventTemplate.objects.create(
            name='API Test Template',
            description='Template for API testing',
            creator=self.host_user
        )
        
        # Add test steps
        EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300,
            name='Pictionary Round'
        )
        
        EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=180,
            name='Free Chat'
        )
    
    def teardown_method(self):
        """Clean up after each test method."""
        Room.objects.filter(room_code__startswith='API').delete()
        EventTemplate.objects.filter(name__contains='API Test').delete()
        User.objects.filter(username__in=['host_user', 'participant_user']).delete()
    
    def get_authenticated_client(self, user):
        """Get an authenticated API client for the given user."""
        refresh = RefreshToken.for_user(user)
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        return client
    
    def test_create_room_success(self):
        """Test successful room creation."""
        client = self.get_authenticated_client(self.host_user)
        
        response = client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        
        assert response.status_code == status.HTTP_201_CREATED
        assert 'room_code' in response.data
        assert 'host' in response.data
        assert response.data['host'] == 'host_user'  # host is a string, not an object
        assert response.data['status'] == RoomState.WAITING
        
        # Verify room was created in database
        room = Room.objects.get(room_code=response.data['room_code'])
        assert room.host == self.host_user
        assert room.event_template == self.template
        assert room.status == RoomState.WAITING
    
    def test_create_room_without_authentication(self):
        """Test room creation without authentication fails."""
        response = self.client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })

        # Should return 401 (Unauthorized) or 403 (Forbidden)
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
    
    def test_create_room_invalid_template(self):
        """Test room creation with invalid template ID."""
        client = self.get_authenticated_client(self.host_user)

        response = client.post('/api/rooms/create/', {
            'template_id': 99999  # Non-existent template
        })

        # The view returns 404 for non-existent template, which is also valid
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_404_NOT_FOUND]
    
    def test_create_room_missing_template(self):
        """Test room creation without template ID."""
        client = self.get_authenticated_client(self.host_user)
        
        response = client.post('/api/rooms/create/', {})
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_join_room_success(self):
        """Test successful room joining."""
        # Create a room first
        host_client = self.get_authenticated_client(self.host_user)
        create_response = host_client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        room_code = create_response.data['room_code']
        
        # Join the room as participant
        participant_client = self.get_authenticated_client(self.participant_user)
        response = participant_client.post('/api/rooms/join/', {
            'room_code': room_code
        })
        
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert 'room' in response.data
        assert response.data['room']['room_code'] == room_code
        
        # Verify participant was added to room
        room = Room.objects.get(room_code=room_code)
        participant = RoomParticipant.objects.get(room=room, user=self.participant_user)
        assert participant.state == UserState.JOINED
        assert participant.role == RoomParticipant.ROLE_PARTICIPANT
    
    def test_join_room_without_authentication(self):
        """Test room joining without authentication fails."""
        response = self.client.post('/api/rooms/join/', {
            'room_code': 'TESTROOM'
        })

        # Should return 401 (Unauthorized) or 403 (Forbidden)
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
    
    def test_join_room_invalid_code(self):
        """Test joining room with invalid room code."""
        client = self.get_authenticated_client(self.participant_user)
        
        response = client.post('/api/rooms/join/', {
            'room_code': 'INVALID'
        })
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_join_room_missing_code(self):
        """Test joining room without room code."""
        client = self.get_authenticated_client(self.participant_user)
        
        response = client.post('/api/rooms/join/', {})
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_join_room_already_joined(self):
        """Test joining room when already a participant."""
        # Create room and join once
        host_client = self.get_authenticated_client(self.host_user)
        create_response = host_client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        room_code = create_response.data['room_code']
        
        participant_client = self.get_authenticated_client(self.participant_user)
        participant_client.post('/api/rooms/join/', {'room_code': room_code})
        
        # Try to join again
        response = participant_client.post('/api/rooms/join/', {
            'room_code': room_code
        })
        
        # Should handle gracefully (either success or appropriate error)
        assert response.status_code in [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST]
    
    def test_get_room_details_success(self):
        """Test getting room details."""
        # Create a room
        host_client = self.get_authenticated_client(self.host_user)
        create_response = host_client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        room_code = create_response.data['room_code']
        
        # Get room details
        response = host_client.get(f'/api/rooms/{room_code}/')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['room_code'] == room_code
        assert response.data['host'] == 'host_user'  # host is a string, not an object
        assert 'participants' in response.data
        assert 'event_template' in response.data
    
    def test_get_room_details_invalid_code(self):
        """Test getting details for non-existent room."""
        client = self.get_authenticated_client(self.host_user)
        
        response = client.get('/api/rooms/INVALID/')
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    def test_subscription_limits_room_creation(self):
        """Test that subscription levels affect room limits."""
        # Test Free user limits
        free_user = User.objects.create_user(
            username='free_test_user',
            password='testpass123',
            subscription_level='Free'
        )
        
        free_template = EventTemplate.objects.create(
            name='Free Template',
            creator=free_user
        )
        
        client = self.get_authenticated_client(free_user)
        response = client.post('/api/rooms/create/', {
            'template_id': free_template.id
        })
        
        if response.status_code == status.HTTP_201_CREATED:
            room = Room.objects.get(room_code=response.data['room_code'])
            # Free users should have limited room capacity
            assert room.max_participants <= 10
        
        # Cleanup
        free_user.delete()
        free_template.delete()
