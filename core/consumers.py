import json
import logging
import async<PERSON>

from typing import Optional
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

from .event_handlers import BaseEventHandler, EventHandlerFactory
from .models import Room, User, RoomParticipant, RoomState, UserState
from .services.room_manager import room_manager
from .exceptions import handle_room_exception, RoomSystemException
from events.models import EventStep
from .utils import advance_to_next_step, save_room

logger = logging.getLogger(__name__)

# --- 新增 ---: 优化后的数据库查询方法
@database_sync_to_async
def get_room_with_host(self, room_code: str) -> Optional[Room]:
    """
    高效地从数据库获取房间及其关联的房主信息。
    """
    try:
        # 关键修复: 使用 select_related('host') 来执行SQL JOIN，一次性获取房主对象。
        return Room.objects.select_related('host').get(room_code=room_code)
    except Room.DoesNotExist:
        return None

class RoomConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer_task = None
        self.current_handler: Optional[BaseEventHandler] = None
        self.room: Optional[Room] = None
        self.user: Optional[User] = None
        self.heartbeat_task = None
        self.heartbeat_interval = 30  # 心跳间隔（秒）
        self.session_timeout_task = None
        self.session_timeout = 30 * 60  # 30分钟会话超时
        self.last_activity = None

    # --- 新增 ---: 优化后的数据库查询方法
    @database_sync_to_async
    def get_room_with_host(self, room_code: str) -> Optional[Room]:
        """
        高效地从数据库获取房间及其关联的房主信息。
        """
        try:
            # 关键修复: 使用 select_related('host') 来执行SQL JOIN，一次性获取房主对象。
            return Room.objects.select_related('host').get(room_code=room_code)
        except Room.DoesNotExist:
            return None

    async def connect(self):
        """处理WebSocket连接"""
        self.user = self.scope['user']
        if not self.user or not self.user.is_authenticated:
            logger.warning("未认证的用户尝试连接WebSocket")
            await self.close(code=4001)  # 认证失败
            return

        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'

        try:
            # 使用新的房间管理器获取房间
            self.room = await room_manager.get_room(self.room_code)

            # 检查房间是否允许连接
            if self.room.status == RoomState.CLOSED:
                await self.send_error("房间已关闭")
                await self.close(code=4002)  # 房间已关闭
                return

            # 用户自动加入房间（如果尚未加入）
            success, message = await room_manager.join_room(self.room_code, self.user)
            if not success and "已在此房间中" not in message:
                await self.send_error(message)
                await self.close(code=4003)  # 加入房间失败
                return

            # 加入WebSocket组
            await self.channel_layer.group_add(self.room_group_name, self.channel_name)
            await self.accept()

            # 初始化会话管理
            self.last_activity = timezone.now()
            self.session_timeout_task = asyncio.create_task(self._session_timeout_loop())

            # 启动心跳任务（如果是房主）
            if self.room.host_id == self.user.id:
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())

            logger.info(f"用户 {self.user.username} 成功连接到房间 {self.room_code}")

            # 广播用户加入消息
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'broadcast.user.joined',
                    'payload': {
                        'username': self.user.username,
                        'message': f'{self.user.username} 加入了房间。'
                    }
                }
            )

        except RoomSystemException as e:
            await self.send_error(e.message)
            await self.close(code=4004)  # 房间系统错误
        except Exception as e:
            logger.error(f"连接到房间 {self.room_code} 时发生错误: {e}", exc_info=True)
            await self.send_error("连接失败，请稍后重试")
            await self.close(code=4000)  # 通用错误

    async def disconnect(self, close_code):
        """处理WebSocket断开连接"""
        logger.info(f"用户 {self.user.username if self.user else 'Unknown'} 断开连接，代码: {close_code}")

        try:
            # 清理心跳任务
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass

            # 清理会话超时任务
            if self.session_timeout_task:
                self.session_timeout_task.cancel()
                try:
                    await self.session_timeout_task
                except asyncio.CancelledError:
                    pass

            # 清理定时器任务
            if self.timer_task and not self.timer_task.done():
                self.timer_task.cancel()
                try:
                    await self.timer_task
                except asyncio.CancelledError:
                    pass

            # 清理当前处理器
            if self.current_handler:
                try:
                    await self.current_handler.cleanup()
                except Exception as e:
                    logger.error(f"清理处理器时发生错误: {e}")

            # 用户离开房间
            if self.user and hasattr(self, 'room_code'):
                success, message = await room_manager.leave_room(self.room_code, self.user)
                if success:
                    logger.info(f"用户 {self.user.username} 成功离开房间 {self.room_code}")

            # 广播用户离开消息并离开房间组
            if hasattr(self, 'room_group_name') and self.user:
                try:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'broadcast.user.left',
                            'payload': {
                                'username': self.user.username,
                                'message': f'{self.user.username} 离开了房间。'
                            }
                        }
                    )
                    await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
                    logger.info(f"用户 {self.user.username} 从房间 {self.room_code} 断开连接")
                except Exception as e:
                    logger.error(f"从房间 {self.room_code} 断开时发生错误: {e}")
        except Exception as e:
            logger.error(f"处理断开连接时出错: {e}", exc_info=True)

    async def _heartbeat_loop(self):
        """房主心跳循环"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                if self.room and self.user and self.room.host_id == self.user.id:
                    room_manager.update_host_heartbeat(self.room_code)
                else:
                    break  # 不再是房主，停止心跳
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳循环出错: {e}", exc_info=True)
                break

    async def receive(self, text_data):
        # 更新活动时间
        self.update_activity()

        try:
            data = json.loads(text_data)
            action = data.get('action')
            payload = data.get('payload', {})

            logger.debug(f"收到来自用户 {self.user.username} 的指令 '{action}'")

            action_handlers = {
                'next_step': self.handle_next_step,
                'send_message': self.handle_chat_message,
                'restart_game': self.handle_restart_game,
                'send_drawing': self.handle_send_drawing,
                'set_ready': self.handle_set_ready,
                'kick_player': self.handle_kick_player,
                'force_start': self.handle_force_start,
                'end_game': self.handle_end_game,
                'heartbeat': self.handle_heartbeat,
            }

            handler = action_handlers.get(action)
            if handler:
                await handler(payload)
            elif self.current_handler and await self.current_handler.handle_custom_action(action, self.user, payload):
                return
            else:
                logger.warning(f"未知的指令 '{action}' 来自用户 {self.user.username}")
                await self.send_error(f"未知的指令: {action}")

        except json.JSONDecodeError as e:
            logger.error(f"无效的JSON格式: {e}")
            await self.send_error("无效的消息格式")
        except RoomSystemException as e:
            logger.warning(f"房间系统异常: {e.message}")
            await self.send_error(e.message)
        except Exception as e:
            logger.error(f"处理消息时发生严重错误: {e}", exc_info=True)
            await self.send_error("服务器内部错误")

    async def handle_next_step(self, payload):
        logger.info(f"用户 {self.user.username} 请求进入下一环节")

        # --- 关键修复 ---:
        # 1. self.room.host 现在是可靠的，因为在 connect 时已使用 select_related 加载。
        # 2. 直接比较用户ID，这是最快、最可靠的方式。
        if not self.room or self.room.host.id != self.user.id:
            logger.warning(f"权限不足: 用户 {self.user.username} 尝试在不属于自己的房间 {self.room_code} 中推进环节。")
            await self.send_error('只有房主才能开始下一环节。')
            return

        logger.info(f"房主 {self.user.username} 权限验证通过，开始推进环节。")
        
        next_step = await advance_to_next_step(self.room)
        if not next_step:
            logger.info(f"房间 {self.room_code} 的所有环节已结束。")
            await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast.event.over'})
            return

        logger.info(f"开始环节 {next_step.order} ({next_step.step_type})")

        if self.current_handler:
            await self.current_handler.cleanup()
        self.current_handler = EventHandlerFactory.create_handler(next_step.step_type, self.room_code, self)

        if not self.current_handler:
            error_msg = f"不支持的环节类型: {next_step.step_type}"
            logger.error(error_msg)
            await self.send_error(error_msg)
            return

        game_data, error = await self.current_handler.start_step(self.room, next_step)
        if error:
            logger.error(f"启动环节失败: {error}")
            await self.send_error(error)
            return

        await self.channel_layer.group_send(self.room_group_name, {
            'type': 'broadcast.step.start',
            'payload': game_data
        })

        await self.start_step_timer(next_step.duration)

    async def handle_chat_message(self, payload):
        """
        处理聊天消息，并广播给房间内的所有人。
        """
        message = payload.get('message', '').strip()
        if not message:
            return

        # 如果当前环节处理器需要处理消息（例如，你画我猜的猜词阶段），则优先处理
        if self.current_handler and await self.current_handler.handle_message(self.user, payload):
            return

        # 否则，作为普通聊天消息广播
        await self.channel_layer.group_send(self.room_group_name, {
            'type': 'broadcast.chat.message',
            'payload': {'message': message, 'sender': self.user.username}
        })

    # --- 新增 ---: 绘图数据的专属处理器
    async def handle_send_drawing(self, payload):
        """
        处理绘图数据，并将其广播给除发送者外的所有人。
        """
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'broadcast.drawing.data',
                'payload': payload, # 直接转发前端发来的payload
                'sender_channel': self.channel_name # 标记发送者，用于避免回显
            }
        )
            
    async def handle_restart_game(self, payload):
        """Handle request to restart a game."""
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting restart in room {self.room_code}")

            room = await get_room_with_host(self.room_code)
            if not room:
                await self.send_error('房间不存在。')
                return

            # Check if user is host
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user:
                await self.send_error('只有房主才能重新开始游戏。')
                return

            # 让当前处理器处理重启
            if self.current_handler:
                game_data, error = await self.current_handler.handle_restart(user, payload)
                if error:
                    await self.send_error(error)
                    return

                if game_data:
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_start',
                        'payload': game_data
                    })

                    # 重新启动计时器
                    current_step = await database_sync_to_async(
                        lambda: room.event_template.steps.filter(order=room.current_step_order).first()
                    )()
                    if current_step:
                        await self.start_step_timer(current_step.duration)
            else:
                await self.send_error("当前没有活动的环节可以重启。")

        except Exception as e:
            logger.error(f"Error handling restart game from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("重新开始游戏时发生错误。")

    async def start_step_timer(self, duration_seconds):
        """Start a timer for the current step."""
        # Cancel any existing timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Start new timer
        self.timer_task = asyncio.create_task(self._step_timer(duration_seconds))

    async def _step_timer(self, duration_seconds):
        """Timer coroutine that handles step timeout."""
        try:
            await asyncio.sleep(duration_seconds)
            # Time's up - end the current step
            await self.handle_step_timeout()
        except asyncio.CancelledError:
            logger.info(f"Timer cancelled for room {self.room_code}")
            raise
        except Exception as e:
            logger.error(f"Error in step timer for room {self.room_code}: {e}")

    async def handle_step_timeout(self):
        """Handle when a step times out."""
        try:
            logger.info(f"Step timeout for room {self.room_code}")

            # 让当前处理器处理超时
            if self.current_handler:
                await self.current_handler.handle_timeout()
            else:
                # 如果没有处理器，执行默认的超时处理
                room = await get_room_with_host(self.room_code)
                if room:
                    room.status = Room.STATUS_WAITING
                    await save_room(room)
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_step_timeout',
                        'payload': {'room_status': room.status}
                    })

        except Exception as e:
            logger.error(f"Error handling step timeout for room {self.room_code}: {e}")

    async def send_error(self, message):
        """Sends an error message back to the originating client."""
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- BROADCAST HANDLERS ---
    async def broadcast_step_start(self, event):
        payload = event['payload'].copy()
        # "你画我猜"环节，对非绘画者隐藏单词
        if payload.get('step_info', {}).get('step_type') == EventStep.STEP_GAME_PICTIONARY:
            if self.user.username != payload.get('drawer'):
                word = payload.get('word', '')
                payload['word'] = " ".join(["_" for char in word if char != ' '])
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event):
        await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))

    async def broadcast_drawing_data(self, event):
        # --- 新增 ---: 关键逻辑，避免将绘图数据发回给发送者自己
        if self.channel_name != event.get('sender_channel'):
            await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))

    async def broadcast_round_over(self, event):
        await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))

    async def broadcast_step_timeout(self, event):
        await self.send(text_data=json.dumps({'type': 'step_timeout', 'payload': event['payload']}))

    async def broadcast_event_over(self, event):
        await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))
    
    # --- 新增 ---: 用户加入/离开的专属广播处理器
    async def broadcast_user_joined(self, event):
        await self.send(text_data=json.dumps({'type': 'user_joined', 'payload': event['payload']}))

    async def broadcast_user_left(self, event):
        await self.send(text_data=json.dumps({'type': 'user_left', 'payload': event['payload']}))

    # === 新增的房间管理方法 ===

    async def handle_set_ready(self, payload):
        """处理用户准备状态设置"""
        try:
            # 更新用户状态为准备就绪
            success = await self._update_user_state(self.user, UserState.READY)
            if success:
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'broadcast.user.ready',
                        'payload': {
                            'username': self.user.username,
                            'ready': True
                        }
                    }
                )
                logger.info(f"用户 {self.user.username} 设置为准备状态")
            else:
                await self.send_error("设置准备状态失败")
        except Exception as e:
            logger.error(f"处理准备状态时出错: {e}", exc_info=True)
            await self.send_error("设置准备状态失败")

    async def handle_kick_player(self, payload):
        """处理踢出玩家请求（仅房主）"""
        if not self.room or self.room.host_id != self.user.id:
            await self.send_error("只有房主可以踢出玩家")
            return

        target_username = payload.get('username')
        if not target_username:
            await self.send_error("请指定要踢出的玩家")
            return

        try:
            # 这里需要实现踢出逻辑
            # 暂时返回成功消息
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'broadcast.player.kicked',
                    'payload': {
                        'username': target_username,
                        'kicked_by': self.user.username
                    }
                }
            )
            logger.info(f"房主 {self.user.username} 踢出了玩家 {target_username}")
        except Exception as e:
            logger.error(f"踢出玩家时出错: {e}", exc_info=True)
            await self.send_error("踢出玩家失败")

    async def handle_force_start(self, payload):
        """处理强制开始游戏请求（仅房主）"""
        if not self.room or self.room.host_id != self.user.id:
            await self.send_error("只有房主可以强制开始游戏")
            return

        try:
            # 转换房间状态到活跃状态
            success, message = await room_manager.transition_room_state(
                self.room_code, RoomState.ACTIVE, self.user
            )
            if success:
                # 将未准备的用户设置为观战状态
                await self._set_unready_users_to_spectating()

                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'broadcast.game.force_started',
                        'payload': {
                            'message': '房主强制开始了游戏',
                            'started_by': self.user.username
                        }
                    }
                )
                logger.info(f"房主 {self.user.username} 强制开始了游戏")
            else:
                await self.send_error(message)
        except Exception as e:
            logger.error(f"强制开始游戏时出错: {e}", exc_info=True)
            await self.send_error("强制开始游戏失败")

    async def handle_end_game(self, payload):
        """处理结束游戏请求（仅房主）"""
        if not self.room or self.room.host_id != self.user.id:
            await self.send_error("只有房主可以结束游戏")
            return

        try:
            # 转换房间状态到复盘状态
            success, message = await room_manager.transition_room_state(
                self.room_code, RoomState.REVIEW, self.user
            )
            if success:
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'broadcast.game.ended',
                        'payload': {
                            'message': '游戏已结束，进入复盘阶段',
                            'ended_by': self.user.username
                        }
                    }
                )
                logger.info(f"房主 {self.user.username} 结束了游戏")
            else:
                await self.send_error(message)
        except Exception as e:
            logger.error(f"结束游戏时出错: {e}", exc_info=True)
            await self.send_error("结束游戏失败")

    async def handle_heartbeat(self, payload):
        """处理心跳请求"""
        if self.room and self.user and self.room.host_id == self.user.id:
            room_manager.update_host_heartbeat(self.room_code)

        # 更新活动时间
        self.update_activity()

        # 发送心跳响应
        await self.send(text_data=json.dumps({
            'type': 'heartbeat_response',
            'payload': {'timestamp': timezone.now().isoformat()}
        }))

    @database_sync_to_async
    def _update_user_state(self, user: User, new_state: UserState) -> bool:
        """更新用户在房间中的状态"""
        try:
            participant = RoomParticipant.objects.get(
                room__room_code=self.room_code,
                user=user,
                is_active=True
            )
            participant.state = new_state
            participant.save()
            return True
        except RoomParticipant.DoesNotExist:
            return False

    @database_sync_to_async
    def _set_unready_users_to_spectating(self):
        """将未准备的用户设置为观战状态"""
        RoomParticipant.objects.filter(
            room__room_code=self.room_code,
            is_active=True,
            state__in=[UserState.JOINED]
        ).update(state=UserState.SPECTATING)

    def update_activity(self):
        """更新用户活动时间"""
        self.last_activity = timezone.now()

    async def _session_timeout_loop(self):
        """会话超时检查循环"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次

                if self.last_activity:
                    inactive_time = (timezone.now() - self.last_activity).total_seconds()

                    if inactive_time > self.session_timeout:
                        logger.info(f"用户 {self.user.username} 会话超时，自动断开连接")

                        # 发送超时通知
                        await self.send(text_data=json.dumps({
                            'type': 'session_timeout',
                            'payload': {
                                'message': '由于长时间无活动，您的会话已过期',
                                'timeout_minutes': self.session_timeout // 60,
                                'redirect_to_login': True
                            }
                        }))

                        # 等待一秒让消息发送完成，然后关闭连接
                        await asyncio.sleep(1)
                        await self.close(code=4005)  # 会话超时
                        break

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"会话超时检查出错: {e}", exc_info=True)
                break

