# core/views.py

import uuid
import logging
import asyncio
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated, AllowAny

# 确保引入了所有需要的模型和序列化器
from .models import User, Room, RoomParticipant, RoomState
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer
from .services.room_manager import room_manager
from .exceptions import handle_room_exception, RoomSystemException
from events.models import EventTemplate, EventStep # 确保引入 EventStep
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger(__name__)


class APIRootView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"message": "Welcome to the Tuanzi API!"})

class HealthCheckView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"status": "ok"}, status=status.HTTP_200_OK)

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

class HealthCheckView(APIView):
    """
    健康检查端点
    用于验证用户认证状态和会话有效性
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """返回用户认证状态"""
        logger.debug(f"健康检查请求，用户: {request.user.username}")

        return Response({
            'status': 'ok',
            'authenticated': True,
            'user': request.user.username,
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)


class RoomCreateView(APIView):
    """
    使用手写的APIView来创建房间，以完全控制创建和返回的逻辑。
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """创建新房间 - 使用新的房间管理器"""
        logger.info(f"收到创建房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试创建房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        template_id = request.data.get('template_id')
        if not template_id:
            logger.warning(f"用户 {request.user.username} 创建房间时未提供模板ID")
            return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            template = EventTemplate.objects.get(id=template_id)
            logger.info(f"找到模板: {template.name} (ID: {template_id})")
        except EventTemplate.DoesNotExist:
            logger.warning(f"用户 {request.user.username} 尝试使用不存在的模板ID: {template_id}")
            return Response({"error": "Template not found."}, status=status.HTTP_404_NOT_FOUND)

        host = request.user

        # 订阅等级检查逻辑
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            premium_steps = template.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
            if premium_steps.exists():
                premium_step_names = [step.get_step_type_display() for step in premium_steps]
                logger.info(f"免费用户 {host.username} 尝试使用付费模板，包含付费环节: {premium_step_names}")
                return Response({
                    "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                    "premium_steps": premium_step_names,
                    "upgrade_required": True
                }, status=status.HTTP_403_FORBIDDEN)

        try:
            logger.info(f"开始为用户 {host.username} 创建房间，使用模板: {template.name}")

            # 使用同步方法调用房间管理器
            new_room = room_manager.create_room_sync(host=host, template_id=template_id)
            logger.info(f"房间创建成功: {new_room.room_code}, 房主: {host.username}")

            # 序列化并返回房间数据
            serializer = RoomSerializer(new_room)
            logger.info(f"返回房间数据给用户 {host.username}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {host.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"创建房间时发生未预期错误: {e}, 用户: {host.username}", exc_info=True)
            return Response({
                "error": "创建房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class JoinRoomView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """用户加入房间 - 使用新的房间管理器"""
        logger.info(f"收到加入房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试加入房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        room_code = request.data.get('room_code')
        if not room_code:
            logger.warning(f"用户 {request.user.username} 加入房间时未提供房间代码")
            return Response(
                {"error": "Room code is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        logger.info(f"用户 {user.username} 尝试加入房间: {room_code}")

        try:
            # 使用同步方法调用房间管理器
            success, message = room_manager.join_room_sync(room_code, user)

            if success:
                logger.info(f"用户 {user.username} 成功加入房间 {room_code}")
                # 获取房间信息并返回
                room = room_manager.get_room_sync(room_code)
                serializer = RoomSerializer(room)
                return Response({
                    "message": message,
                    "room": serializer.data
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"用户 {user.username} 加入房间 {room_code} 失败: {message}")
                # 根据错误消息确定HTTP状态码
                if "不存在" in message:
                    status_code = status.HTTP_404_NOT_FOUND
                elif "已满" in message or "过期" in message or "不允许" in message or "已关闭" in message:
                    status_code = status.HTTP_403_FORBIDDEN
                else:
                    status_code = status.HTTP_400_BAD_REQUEST

                return Response({"error": message}, status=status_code)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {user.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"加入房间时发生未预期错误: {e}, 用户: {user.username}", exc_info=True)
            return Response({
                "error": "加入房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomDetailView(APIView):
    """
    获取房间详情
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, room_code, *args, **kwargs):
        """获取房间详情"""
        logger.info(f"收到获取房间详情请求，房间: {room_code}, 用户: {request.user.username}")

        try:
            # 获取房间信息
            room = room_manager.get_room_sync(room_code)

            # 序列化并返回房间数据
            serializer = RoomSerializer(room)
            logger.info(f"返回房间 {room_code} 详情给用户 {request.user.username}")
            return Response(serializer.data, status=status.HTTP_200_OK)

        except RoomSystemException as e:
            logger.error(f"获取房间详情时发生房间系统异常: {e.message}, 房间: {room_code}")
            return Response(handle_room_exception(e), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取房间详情时发生未预期错误: {e}, 房间: {room_code}", exc_info=True)
            return Response({
                "error": "获取房间详情时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SubscriptionManagementView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        return Response({
            'current_level': user.subscription_level,
            'username': user.username,
            'subscription_info': {
                'Free': {'max_participants': 10, 'duration_hours': 2},
                'Pro': {'max_participants': 500, 'duration_hours': 24},
                'Max': {'max_participants': 2000, 'duration_hours': 72},
            }
        })

    def post(self, request, *args, **kwargs):
        target_level = request.data.get('target_level')
        is_debug = request.data.get('is_debug', False)
        if target_level not in [User.SUBSCRIPTION_FREE, User.SUBSCRIPTION_PRO, User.SUBSCRIPTION_MAX]:
            return Response(
                {"error": "Invalid subscription level"},
                status=status.HTTP_400_BAD_REQUEST
            )
        user = request.user
        if is_debug and hasattr(request, 'META') and request.META.get('HTTP_X_DEBUG_MODE'):
            user.subscription_level = target_level
            user.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'message': f'Debug: Subscription level changed to {target_level}',
                'new_level': target_level,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh)
            })
        return Response({
            'message': 'Payment integration not implemented yet',
        }, status=status.HTTP_501_NOT_IMPLEMENTED)
