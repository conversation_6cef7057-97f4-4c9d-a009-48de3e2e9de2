["test/smoke/test_server_startup.py::TestServerStartup::test_database_connection", "test/smoke/test_server_startup.py::TestServerStartup::test_database_tables_exist", "test/smoke/test_server_startup.py::TestServerStartup::test_django_settings_loaded", "test/smoke/test_server_startup.py::TestServerStartup::test_enums_are_defined", "test/smoke/test_server_startup.py::TestServerStartup::test_models_can_be_imported", "test/smoke/test_server_startup.py::TestServerStartup::test_room_model_fields", "test/smoke/test_server_startup.py::TestServerStartup::test_room_participant_model_fields", "test/smoke/test_server_startup.py::TestServerStartup::test_user_model_fields", "test/smoke/test_server_startup.py::test_can_create_basic_objects", "test/smoke/test_server_startup.py::test_subscription_levels"]